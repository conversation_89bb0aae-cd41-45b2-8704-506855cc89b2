import { zValidator } from "@hono/zod-validator";
import { Context } from "hono";
import { z } from "zod";
import { AppContext } from "../../../../types";
import { getDbClient } from "../../../../database-service";
import { eq, and } from "drizzle-orm";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { HTTPException } from "hono/http-exception";
import { decryptToken, encryptToken } from "../../../../token-utils";
import { triggerInitialSync } from "../../../../sync-utils";

const selectPageValidator = z.object({
  pageId: z.string().min(1, "Page ID is required"),
});

export const selectPageBodyValidator = zValidator("json", selectPageValidator);

export const selectFacebookPage = async (
  c: Context<
    AppContext,
    "/manage/projects/:projectId/connections/:connectionId/select-page",
    {
      in: {
        json: z.infer<typeof selectPageValidator>;
      };
      out: {
        json: z.infer<typeof selectPageValidator>;
      };
    }
  >
) => {
  const requestedProjectId = c.req.param("projectId");
  const requestedConnectionId = c.req.param("connectionId");
  const organizationId = c.var.organizationId;
  const { pageId } = c.req.valid("json");

  if (!organizationId) {
    throw new HTTPException(401, {
      message: "Organization ID not found",
    });
  }

  const db = getDbClient(c.env.DB);

  try {
    // Get the connection with project verification
    const connections = await db
      .select()
      .from(schema.platformConnections)
      .innerJoin(
        schema.projects,
        eq(schema.platformConnections.projectId, schema.projects.id)
      )
      .where(
        and(
          eq(schema.platformConnections.id, requestedConnectionId),
          eq(schema.platformConnections.projectId, requestedProjectId),
          eq(schema.projects.organizationId, organizationId)
        )
      )
      .limit(1)
      .all();

    if (connections.length < 1) {
      throw new HTTPException(404, {
        message: "Connection not found",
      });
    }

    const connection = connections[0].platform_connections;

    // Verify this is a Facebook connection
    if (connection.platform !== "facebook") {
      throw new HTTPException(400, {
        message: "This endpoint is only for Facebook connections",
      });
    }

    if (!connection.pendingPageSelection) {
      throw new HTTPException(400, {
        message: "This connection is not pending page selection",
      });
    }

    if (!connection.accessTokenEncrypted) {
      throw new HTTPException(400, {
        message: "No access token found for this connection",
      });
    }

    // Get the user's access token to fetch page access token
    const userAccessToken = decryptToken(
      connection.accessTokenEncrypted,
      c.env.ENCRYPTION_KEY
    );

    // Fetch user's pages with access tokens
    const pagesResponse = await fetch(
      `https://graph.facebook.com/v19.0/me/accounts?fields=id,name,about,fan_count,followers_count,picture.type(large),access_token&access_token=${userAccessToken}`
    );

    if (!pagesResponse.ok) {
      const errorText = await pagesResponse.text();
      console.error("Failed to fetch user pages:", errorText);
      throw new HTTPException(400, {
        message: "Failed to fetch user pages from Facebook",
      });
    }

    const pagesData = (await pagesResponse.json()) as { data?: any[] };
    const selectedPage = pagesData.data?.find(
      (page: any) => page.id === pageId
    );

    if (!selectedPage || !selectedPage.access_token) {
      throw new HTTPException(400, {
        message: "Page not found or no access token available for this page",
      });
    }

    // Encrypt the page access token
    const encryptedPageAccessToken = await encryptToken(
      selectedPage.access_token,
      c.env.ENCRYPTION_KEY
    );

    // Update connection with selected page and page access token
    await db
      .update(schema.platformConnections)
      .set({
        platformAccountId: selectedPage.id,
        platformAccountName: selectedPage.name,
        platformAccountDescription: selectedPage.about,
        platformAccountFollowers:
          selectedPage.followers_count || selectedPage.fan_count,
        platformAccountProfilePictureUrl: selectedPage.picture?.data?.url,
        accessTokenEncrypted: encryptedPageAccessToken, // Store page access token instead of user token
        isConnected: true, // Now fully connected
        pendingPageSelection: false,
        availablePages: null, // Clear the available pages data
        lastCheckedAt: new Date(),
      })
      .where(eq(schema.platformConnections.id, requestedConnectionId));

    // Trigger initial sync now that page is selected
    await triggerInitialSync(c.env, requestedConnectionId);

    return c.json({
      success: true,
      message: "Facebook page selected successfully",
      selectedPage: {
        id: selectedPage.id,
        name: selectedPage.name,
        description: selectedPage.about,
        followers_count: selectedPage.followers_count || selectedPage.fan_count,
        profile_picture_url: selectedPage.picture?.data?.url,
      },
    });
  } catch (e) {
    console.error(
      `Failed to select Facebook page for connection ${requestedConnectionId}:`,
      e
    );

    if (e instanceof HTTPException) {
      throw e;
    }

    throw new HTTPException(500, {
      message: "Failed to select Facebook page",
    });
  }
};
